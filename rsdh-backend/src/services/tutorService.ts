import { prisma } from '../lib/prisma';
import { Tutor<PERSON>ro<PERSON>le, <PERSON><PERSON>Education, Tutor<PERSON>areer, TutorAvailability } from '@prisma/client';

export interface CreateTutorApplicationData {
  userId: string;
  title?: string;
  bio?: string;
  rate?: number; // Deprecated, use hourlyRate instead
  hourlyRate?: number;
  halfHourRate?: number;
  isFree?: boolean;
  currency?: string;
  education: {
    degree: string;
    fieldOfStudy: string;
    institution: string;
    startYear: number;
    endYear?: number;
    description?: string;
  }[];
  career: {
    title: string;
    company: string;
    startYear: number;
    endYear?: number;
    current: boolean;
    description?: string;
  }[];
}

export interface UpdateTutorProfileData {
  title?: string;
  bio?: string;
  rate?: number; // Deprecated, use hourlyRate instead
  hourlyRate?: number;
  halfHourRate?: number;
  isFree?: boolean;
  currency?: string;
}

export interface TutorWithDetails extends TutorProfile {
  user: {
    id: string;
    email: string;
    name: string | null;
    avatar: string | null;
  };
  education: TutorEducation[];
  career: <PERSON><PERSON><PERSON><PERSON><PERSON>[];
  availability: TutorAvailability[];
  _count: {
    reviews: number;
    appointments: number;
  };
  averageRating?: number;
}

export class TutorService {
  /**
   * Apply to become a tutor
   */
  static async applyToBecomeTutor(data: CreateTutorApplicationData): Promise<TutorProfile> {
    // Check if user already has a tutor profile
    const existingProfile = await prisma.tutorProfile.findUnique({
      where: { userId: data.userId }
    });

    if (existingProfile) {
      throw new Error('User already has a tutor profile');
    }

    // Validate user exists
    const user = await prisma.user.findUnique({
      where: { id: data.userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Create tutor profile with education and career in a transaction
    const tutorProfile = await prisma.$transaction(async (tx) => {
      // Create tutor profile
      const profile = await tx.tutorProfile.create({
        data: {
          userId: data.userId,
          title: data.title,
          bio: data.bio,
          rate: data.rate || 0, // Keep for backward compatibility
          hourlyRate: data.hourlyRate || data.rate || 0,
          halfHourRate: data.halfHourRate || (data.hourlyRate || data.rate || 0) / 2,
          isFree: data.isFree || false,
          currency: data.currency || 'CNY',
          status: 'pending'
        }
      });

      // Create education records
      if (data.education && data.education.length > 0) {
        await tx.tutorEducation.createMany({
          data: data.education.map(edu => ({
            tutorId: profile.id,
            ...edu
          }))
        });
      }

      // Create career records
      if (data.career && data.career.length > 0) {
        await tx.tutorCareer.createMany({
          data: data.career.map(career => ({
            tutorId: profile.id,
            ...career
          }))
        });
      }

      return profile;
    });

    return tutorProfile;
  }

  /**
   * Get tutor profile by user ID
   */
  static async getTutorByUserId(userId: string): Promise<TutorWithDetails | null> {
    const tutor = await prisma.tutorProfile.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true
          }
        },
        education: {
          orderBy: { startYear: 'desc' }
        },
        career: {
          orderBy: { startYear: 'desc' }
        },
        availability: {
          orderBy: { dayOfWeek: 'asc' }
        },
        _count: {
          select: {
            reviews: true,
            appointments: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      }
    });

    if (!tutor) {
      return null;
    }

    // Calculate average rating
    const averageRating = tutor.reviews.length > 0
      ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
      : 0;

    // Remove reviews from the result and add averageRating
    const { reviews, ...tutorWithoutReviews } = tutor;

    return {
      ...tutorWithoutReviews,
      averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
    };
  }

  /**
   * Get tutor profile by tutor ID
   */
  static async getTutorById(tutorId: string): Promise<TutorWithDetails | null> {
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true
          }
        },
        education: {
          orderBy: { startYear: 'desc' }
        },
        career: {
          orderBy: { startYear: 'desc' }
        },
        availability: {
          orderBy: { dayOfWeek: 'asc' }
        },
        _count: {
          select: {
            reviews: true,
            appointments: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      }
    });

    if (!tutor) {
      return null;
    }

    // Calculate average rating
    const averageRating = tutor.reviews.length > 0
      ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
      : 0;

    // Remove reviews from the result and add averageRating
    const { reviews, ...tutorWithoutReviews } = tutor;

    return {
      ...tutorWithoutReviews,
      averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
    };
  }

  /**
   * Update tutor profile
   */
  static async updateTutorProfile(tutorId: string, data: UpdateTutorProfileData): Promise<TutorProfile> {
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    return await prisma.tutorProfile.update({
      where: { id: tutorId },
      data: {
        title: data.title,
        bio: data.bio,
        rate: data.rate,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Get all tutors with pagination and filtering
   */
  static async getAllTutors(options: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  } = {}): Promise<{
    tutors: TutorWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, status, search } = options;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { bio: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } }
      ];
    }

    const [tutors, total] = await Promise.all([
      prisma.tutorProfile.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              avatar: true
            }
          },
          education: {
            orderBy: { startYear: 'desc' }
          },
          career: {
            orderBy: { startYear: 'desc' }
          },
          availability: {
            orderBy: { dayOfWeek: 'asc' }
          },
          _count: {
            select: {
              reviews: true,
              appointments: true
            }
          },
          reviews: {
            select: {
              rating: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.tutorProfile.count({ where })
    ]);

    // Calculate average ratings for all tutors
    const tutorsWithRatings = tutors.map(tutor => {
      const averageRating = tutor.reviews.length > 0
        ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
        : 0;

      const { reviews, ...tutorWithoutReviews } = tutor;

      return {
        ...tutorWithoutReviews,
        averageRating: Math.round(averageRating * 10) / 10
      };
    });

    return {
      tutors: tutorsWithRatings,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Approve or reject tutor application (admin only)
   */
  static async updateTutorStatus(tutorId: string, status: 'approved' | 'rejected'): Promise<TutorProfile> {
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    return await prisma.tutorProfile.update({
      where: { id: tutorId },
      data: {
        status,
        updatedAt: new Date()
      }
    });
  }
}
