import { FastifyInstance } from 'fastify';
import { authenticateUser, requireAdmin, AuthenticatedRequest } from '../middleware/auth';
import { prisma } from '../lib/prisma';

export async function adminRoutes(fastify: FastifyInstance) {
  // ===== DASHBOARD STATISTICS =====

  // Get dashboard overview statistics
  fastify.get('/dashboard/stats', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Dashboard'],
      summary: 'Get dashboard statistics',
      description: 'Get overview statistics for admin dashboard',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Dashboard statistics',
          type: 'object',
          properties: {
            users: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                active: { type: 'integer' },
                newThisMonth: { type: 'integer' },
                growth: { type: 'number' }
              }
            },
            tutors: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                approved: { type: 'integer' },
                pending: { type: 'integer' },
                rejected: { type: 'integer' },
                averageRating: { type: 'number' }
              }
            },
            appointments: {
              type: 'object',
              properties: {
                total: { type: 'integer' },
                completed: { type: 'integer' },
                scheduled: { type: 'integer' },
                cancelled: { type: 'integer' },
                thisMonth: { type: 'integer' }
              }
            },
            payments: {
              type: 'object',
              properties: {
                totalRevenue: { type: 'number' },
                thisMonthRevenue: { type: 'number' },
                totalTransactions: { type: 'integer' },
                averageOrderValue: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      // User statistics
      const [totalUsers, activeUsers, newUsersThisMonth, newUsersLastMonth] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { disabled: false } }),
        prisma.user.count({ where: { createdAt: { gte: startOfMonth } } }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: startOfLastMonth,
              lte: endOfLastMonth
            }
          }
        })
      ]);

      const userGrowth = newUsersLastMonth > 0
        ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100
        : 0;

      // Tutor statistics
      const [totalTutors, approvedTutors, pendingTutors, rejectedTutors] = await Promise.all([
        prisma.tutorProfile.count(),
        prisma.tutorProfile.count({ where: { status: 'approved' } }),
        prisma.tutorProfile.count({ where: { status: 'pending' } }),
        prisma.tutorProfile.count({ where: { status: 'rejected' } })
      ]);

      // Calculate average tutor rating
      const avgRatingResult = await prisma.review.aggregate({
        _avg: { rating: true }
      });
      const averageRating = avgRatingResult._avg.rating || 0;

      // Appointment statistics
      const [totalAppointments, completedAppointments, scheduledAppointments,
             cancelledAppointments, appointmentsThisMonth] = await Promise.all([
        prisma.appointment.count(),
        prisma.appointment.count({ where: { status: 'completed' } }),
        prisma.appointment.count({ where: { status: 'scheduled' } }),
        prisma.appointment.count({ where: { status: 'cancelled' } }),
        prisma.appointment.count({ where: { createdAt: { gte: startOfMonth } } })
      ]);

      // Payment statistics
      const [totalRevenueResult, thisMonthRevenueResult, totalTransactions] = await Promise.all([
        prisma.payment.aggregate({
          _sum: { amount: true },
          where: { status: 'completed' }
        }),
        prisma.payment.aggregate({
          _sum: { amount: true },
          where: {
            status: 'completed',
            createdAt: { gte: startOfMonth }
          }
        }),
        prisma.payment.count({ where: { status: 'completed' } })
      ]);

      const totalRevenue = totalRevenueResult._sum.amount || 0;
      const thisMonthRevenue = thisMonthRevenueResult._sum.amount || 0;
      const averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

      return reply.send({
        users: {
          total: totalUsers,
          active: activeUsers,
          newThisMonth: newUsersThisMonth,
          growth: userGrowth
        },
        tutors: {
          total: totalTutors,
          approved: approvedTutors,
          pending: pendingTutors,
          rejected: rejectedTutors,
          averageRating: Number(averageRating.toFixed(2))
        },
        appointments: {
          total: totalAppointments,
          completed: completedAppointments,
          scheduled: scheduledAppointments,
          cancelled: cancelledAppointments,
          thisMonth: appointmentsThisMonth
        },
        payments: {
          totalRevenue: Number(totalRevenue.toFixed(2)),
          thisMonthRevenue: Number(thisMonthRevenue.toFixed(2)),
          totalTransactions,
          averageOrderValue: Number(averageOrderValue.toFixed(2))
        }
      });

    } catch (error) {
      request.log.error('Error fetching dashboard stats:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch dashboard statistics'
      });
    }
  });

  // Get user growth chart data
  fastify.get('/dashboard/user-growth', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Dashboard'],
      summary: 'Get user growth chart data',
      description: 'Get user registration data for the last 12 months',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          months: { type: 'integer', minimum: 1, maximum: 24, default: 12 }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { months = 12 } = request.query as { months?: number };
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - months + 1, 1);

      const userGrowthData = await prisma.$queryRaw`
        SELECT
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*) as count
        FROM "users"
        WHERE "createdAt" >= ${startDate}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month ASC
      `;

      return reply.send(userGrowthData);

    } catch (error) {
      request.log.error('Error fetching user growth data:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch user growth data'
      });
    }
  });

  // Get appointment trends
  fastify.get('/dashboard/appointment-trends', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Dashboard'],
      summary: 'Get appointment trends',
      description: 'Get appointment booking trends for the last 12 months',
      security: [{ bearerAuth: [] }]
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - 11, 1);

      const appointmentTrends = await prisma.$queryRaw`
        SELECT
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
        FROM "appointments"
        WHERE "createdAt" >= ${startDate}
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month ASC
      `;

      return reply.send(appointmentTrends);

    } catch (error) {
      request.log.error('Error fetching appointment trends:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch appointment trends'
      });
    }
  });

  // ===== TUTOR MANAGEMENT =====

  // Get tutors with low ratings (warning system)
  fastify.get('/tutors/low-rating', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Tutor Management'],
      summary: 'Get tutors with low ratings',
      description: 'Get tutors with average rating below threshold for warning',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          threshold: { type: 'number', minimum: 1, maximum: 5, default: 3.0 },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { threshold = 3.0, page = 1, limit = 10 } = request.query as {
        threshold?: number;
        page?: number;
        limit?: number;
      };

      const offset = (page - 1) * limit;

      // Get tutors with their average ratings
      const tutorsWithLowRating = await prisma.$queryRaw`
        SELECT
          tp.id,
          tp."userId",
          u.name,
          u.email,
          tp.status,
          tp."createdAt",
          COALESCE(AVG(r.rating), 0) as "averageRating",
          COUNT(r.id) as "reviewCount"
        FROM "tutors" tp
        JOIN "users" u ON tp."userId" = u.id
        LEFT JOIN "reviews" r ON tp.id = r."tutorId"
        WHERE tp.status = 'approved'
        GROUP BY tp.id, tp."userId", u.name, u.email, tp.status, tp."createdAt"
        HAVING COUNT(r.id) > 0 AND AVG(r.rating) < ${threshold}
        ORDER BY AVG(r.rating) ASC
        LIMIT ${limit} OFFSET ${offset}
      `;

      const totalCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM (
          SELECT tp.id
          FROM "tutors" tp
          LEFT JOIN "reviews" r ON tp.id = r."tutorId"
          WHERE tp.status = 'approved'
          GROUP BY tp.id
          HAVING COUNT(r.id) > 0 AND AVG(r.rating) < ${threshold}
        ) as subquery
      `;

      return reply.send({
        tutors: tutorsWithLowRating,
        pagination: {
          page,
          limit,
          total: Number((totalCount as any)[0]?.count || 0),
          totalPages: Math.ceil(Number((totalCount as any)[0]?.count || 0) / limit)
        }
      });

    } catch (error) {
      request.log.error('Error fetching low-rating tutors:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch low-rating tutors'
      });
    }
  });

  // Update tutor status (approve/reject/suspend)
  fastify.patch('/tutors/:tutorId/status', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Tutor Management'],
      summary: 'Update tutor status',
      description: 'Approve, reject, or suspend a tutor',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['tutorId'],
        properties: {
          tutorId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        required: ['status'],
        properties: {
          status: {
            type: 'string',
            enum: ['pending', 'approved', 'rejected', 'suspended']
          },
          reason: { type: 'string', description: 'Reason for status change' }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { tutorId } = request.params as { tutorId: string };
      const { status, reason } = request.body as { status: string; reason?: string };

      // Check if tutor exists
      const tutor = await prisma.tutorProfile.findUnique({
        where: { id: tutorId },
        include: { user: true }
      });

      if (!tutor) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Tutor not found'
        });
      }

      // Update tutor status
      const updatedTutor = await prisma.tutorProfile.update({
        where: { id: tutorId },
        data: { status },
        include: { user: true }
      });

      // Log the status change (you might want to create an audit log table)
      request.log.info(`Admin ${request.user?.email} changed tutor ${tutor.user.email} status to ${status}`, {
        adminId: request.user?.id,
        tutorId,
        oldStatus: tutor.status,
        newStatus: status,
        reason
      });

      return reply.send({
        message: 'Tutor status updated successfully',
        tutor: updatedTutor
      });

    } catch (error) {
      request.log.error('Error updating tutor status:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to update tutor status'
      });
    }
  });

  // Get revenue analytics
  fastify.get('/analytics/revenue', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - Analytics'],
      summary: 'Get revenue analytics',
      description: 'Get detailed revenue analytics and payment statistics',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          period: { type: 'string', enum: ['week', 'month', 'quarter', 'year'], default: 'month' },
          months: { type: 'integer', minimum: 1, maximum: 24, default: 12 }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { period = 'month', months = 12 } = request.query as {
        period?: string;
        months?: number;
      };

      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - months + 1, 1);

      let dateFormat = 'month';
      if (period === 'week') dateFormat = 'week';
      else if (period === 'quarter') dateFormat = 'quarter';
      else if (period === 'year') dateFormat = 'year';

      // Revenue trends
      const revenueTrends = await prisma.$queryRaw`
        SELECT
          DATE_TRUNC(${dateFormat}, "createdAt") as period,
          SUM(amount) as revenue,
          COUNT(*) as transactions,
          AVG(amount) as "averageOrderValue"
        FROM "payments"
        WHERE status = 'completed' AND "createdAt" >= ${startDate}
        GROUP BY DATE_TRUNC(${dateFormat}, "createdAt")
        ORDER BY period ASC
      `;

      // Payment method distribution
      const paymentMethods = await prisma.payment.groupBy({
        by: ['paymentMethod'],
        where: {
          status: 'completed',
          createdAt: { gte: startDate }
        },
        _sum: { amount: true },
        _count: true
      });

      // Top earning tutors
      const topTutors = await prisma.$queryRaw`
        SELECT
          tp.id,
          u.name,
          u.email,
          SUM(p.amount) as "totalEarnings",
          COUNT(p.id) as "totalTransactions",
          AVG(p.amount) as "averageOrderValue"
        FROM "payments" p
        JOIN "tutors" tp ON p."tutorId" = tp.id
        JOIN "users" u ON tp."userId" = u.id
        WHERE p.status = 'completed' AND p."createdAt" >= ${startDate}
        GROUP BY tp.id, u.name, u.email
        ORDER BY SUM(p.amount) DESC
        LIMIT 10
      `;

      return reply.send({
        revenueTrends,
        paymentMethods,
        topTutors
      });

    } catch (error) {
      request.log.error('Error fetching revenue analytics:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch revenue analytics'
      });
    }
  });

  // Get system health metrics
  fastify.get('/system/health', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Admin - System'],
      summary: 'Get system health metrics',
      description: 'Get system performance and health indicators',
      security: [{ bearerAuth: [] }]
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Database metrics
      const [
        totalUsers,
        activeUsers24h,
        totalAppointments24h,
        failedPayments24h,
        pendingEducationVerifications,
        pendingCareerVerifications
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            updatedAt: { gte: last24Hours }
          }
        }),
        prisma.appointment.count({
          where: {
            createdAt: { gte: last24Hours }
          }
        }),
        prisma.payment.count({
          where: {
            status: 'failed',
            createdAt: { gte: last24Hours }
          }
        }),
        prisma.educationVerification.count({
          where: { status: 'pending' }
        }),
        prisma.careerVerification.count({
          where: { status: 'pending' }
        })
      ]);

      const pendingVerifications = pendingEducationVerifications + pendingCareerVerifications;

      // Error rates
      const errorRate = failedPayments24h > 0 ?
        (failedPayments24h / (failedPayments24h + totalAppointments24h)) * 100 : 0;

      return reply.send({
        database: {
          totalUsers,
          activeUsers24h,
          connectionStatus: 'healthy' // You might want to implement actual DB health check
        },
        activity: {
          appointments24h: totalAppointments24h,
          activeUsers24h,
          errorRate: Number(errorRate.toFixed(2))
        },
        pending: {
          verifications: pendingVerifications,
          payments: failedPayments24h
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: now.toISOString()
      });

    } catch (error) {
      request.log.error('Error fetching system health:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch system health metrics'
      });
    }
  });
}
