"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
const fastify_1 = __importDefault(require("fastify"));
const cors_1 = __importDefault(require("@fastify/cors"));
const helmet_1 = __importDefault(require("@fastify/helmet"));
// import { Headers, Request } from 'node-fetch';
const env_1 = __importDefault(require("./config/env"));
const swagger_1 = require("./config/swagger");
const prisma_1 = require("./lib/prisma");
const users_1 = require("./routes/users");
const auth_1 = require("./routes/auth");
const authExtended_1 = require("./routes/authExtended");
const tutors_1 = require("./routes/tutors");
const tutorManagement_1 = require("./routes/tutorManagement");
const appointments_1 = require("./routes/appointments");
const reviews_1 = require("./routes/reviews");
const files_1 = require("./routes/files");
const initService_1 = require("./services/initService");
async function createApp() {
    const app = (0, fastify_1.default)({
        logger: {
            level: 'info',
            transport: {
                target: 'pino-pretty',
                options: {
                    translateTime: 'HH:MM:ss Z',
                    ignore: 'pid,hostname',
                },
            },
        },
        disableRequestLogging: process.env.NODE_ENV === 'production',
        trustProxy: true, // Trust proxy headers for secure cookies
    });
    // Register environment variables first
    await app.register(env_1.default);
    // Add prisma to app instance
    app.decorate('prisma', prisma_1.prisma);
    // Configure security headers first
    if (app.config.NODE_ENV === 'production') {
        await app.register(helmet_1.default, {
            contentSecurityPolicy: false, // Disable CSP for now to avoid conflicts
            crossOriginEmbedderPolicy: false, // Required for some auth flows
            crossOriginOpenerPolicy: { policy: 'same-origin' },
            crossOriginResourcePolicy: { policy: 'same-site' },
        });
    }
    await app.register(helmet_1.default, {
        contentSecurityPolicy: false, // Disable CSP for now to avoid conflicts
        crossOriginEmbedderPolicy: false, // Required for some auth flows
        crossOriginOpenerPolicy: { policy: 'same-origin' },
        crossOriginResourcePolicy: { policy: 'same-site' },
    });
    // Configure CORS with settings from environment
    const corsOptions = {
        origin: app.config.NODE_ENV === 'production'
            ? app.config.CORS_ORIGIN?.split(',').map(origin => origin.trim())
            : app.config.CORS_ORIGIN?.split(',').map(origin => origin.trim()) || ['http://localhost:3000', 'http://localhost:8082'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        exposedHeaders: ['Content-Range', 'X-Total-Count'],
        credentials: true,
        maxAge: 600, // 10 minutes
    };
    // Register CORS with the provided options
    await app.register(cors_1.default, corsOptions);
    // Register Swagger before defining routes
    await (0, swagger_1.registerSwagger)(app);
    // Initialize default accounts
    await initService_1.InitService.initializeDefaultAccounts(app);
    // Register user routes
    await app.register(users_1.userRoutes, { prefix: '/api/users' });
    // Register auth routes
    await app.register(auth_1.authRoutes, { prefix: '/api/auth' });
    // Register extended auth routes (phone, WeChat)
    await app.register(authExtended_1.authExtendedRoutes, { prefix: '/api/auth' });
    // Register tutor routes
    await app.register(tutors_1.tutorRoutes, { prefix: '/api/tutors' });
    // Register tutor management routes
    await app.register(tutorManagement_1.tutorManagementRoutes, { prefix: '/api/tutors/manage' });
    // Register appointment routes
    await app.register(appointments_1.appointmentRoutes, { prefix: '/api/appointments' });
    // Register review routes
    await app.register(reviews_1.reviewRoutes, { prefix: '/api/reviews' });
    // Register file routes
    await app.register(files_1.fileRoutes, { prefix: '/api/files' });
    // Health check route with OpenAPI documentation
    app.get('/health', {
        schema: {
            tags: ['Health'],
            summary: 'Health Check',
            description: 'Check the health status of the API server',
            operationId: 'healthCheck',
            security: [], // No auth required for health check
            response: {
                200: {
                    description: 'Successful health check response',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    status: {
                                        type: 'string',
                                        enum: ['ok'],
                                        description: 'Service status',
                                        example: 'ok'
                                    },
                                    timestamp: {
                                        type: 'string',
                                        format: 'date-time',
                                        description: 'Current server time',
                                        example: '2025-06-29T13:15:05.000Z'
                                    },
                                    environment: {
                                        type: 'string',
                                        description: 'Current environment',
                                        example: 'development'
                                    },
                                },
                                required: ['status', 'timestamp', 'environment']
                            }
                        }
                    }
                },
                500: {
                    description: 'Server Error',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    statusCode: { type: 'number' },
                                    error: { type: 'string' },
                                    message: { type: 'string' }
                                }
                            }
                        }
                    }
                }
            },
            openapi: {
                tags: [{ name: 'Health' }]
            }
        },
    }, async () => {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            environment: app.config.NODE_ENV,
        };
    });
    // Example protected route
    app.get('/protected', async (_request, _reply) => {
        // Note: The session logic will be added here in the next step.
        return { message: 'This is a protected route, but still needs session validation.' };
    });
    // Error handling
    app.setErrorHandler((error, request, reply) => {
        const statusCode = error.statusCode || 500;
        // Log the error
        request.log.error({
            req: request,
            res: reply,
            err: error,
            msg: error.message
        }, 'Request error');
        // Send error response
        reply.status(statusCode).send({
            statusCode,
            error: statusCode === 500 ? 'Internal Server Error' : error.name,
            message: statusCode === 500 ? 'An internal server error occurred' : error.message
        });
    });
    // Add type for rawBody
    app.addHook('preValidation', (request, _reply, done) => {
        request.rawBody = request.body;
        done();
    });
    // Handle 404 - Keep this as the last route
    app.setNotFoundHandler((request, reply) => {
        reply.status(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: `Route ${request.method}:${request.url} not found`
        });
    });
    return app;
}
