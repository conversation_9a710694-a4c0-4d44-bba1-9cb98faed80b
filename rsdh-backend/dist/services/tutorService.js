"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TutorService = void 0;
const prisma_1 = require("../lib/prisma");
class TutorService {
    /**
     * Apply to become a tutor
     */
    static async applyToBecomeTutor(data) {
        // Check if user already has a tutor profile
        const existingProfile = await prisma_1.prisma.tutorProfile.findUnique({
            where: { userId: data.userId }
        });
        if (existingProfile) {
            throw new Error('User already has a tutor profile');
        }
        // Validate user exists
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: data.userId }
        });
        if (!user) {
            throw new Error('User not found');
        }
        // Create tutor profile with education and career in a transaction
        const tutorProfile = await prisma_1.prisma.$transaction(async (tx) => {
            // Create tutor profile
            const profile = await tx.tutorProfile.create({
                data: {
                    userId: data.userId,
                    title: data.title,
                    bio: data.bio,
                    rate: data.rate || 0,
                    status: 'pending'
                }
            });
            // Create education records
            if (data.education && data.education.length > 0) {
                await tx.tutorEducation.createMany({
                    data: data.education.map(edu => ({
                        tutorId: profile.id,
                        ...edu
                    }))
                });
            }
            // Create career records
            if (data.career && data.career.length > 0) {
                await tx.tutorCareer.createMany({
                    data: data.career.map(career => ({
                        tutorId: profile.id,
                        ...career
                    }))
                });
            }
            return profile;
        });
        return tutorProfile;
    }
    /**
     * Get tutor profile by user ID
     */
    static async getTutorByUserId(userId) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { userId },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        avatar: true
                    }
                },
                education: {
                    orderBy: { startYear: 'desc' }
                },
                career: {
                    orderBy: { startYear: 'desc' }
                },
                availability: {
                    orderBy: { dayOfWeek: 'asc' }
                },
                _count: {
                    select: {
                        reviews: true,
                        appointments: true
                    }
                },
                reviews: {
                    select: {
                        rating: true
                    }
                }
            }
        });
        if (!tutor) {
            return null;
        }
        // Calculate average rating
        const averageRating = tutor.reviews.length > 0
            ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
            : 0;
        // Remove reviews from the result and add averageRating
        const { reviews, ...tutorWithoutReviews } = tutor;
        return {
            ...tutorWithoutReviews,
            averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
        };
    }
    /**
     * Get tutor profile by tutor ID
     */
    static async getTutorById(tutorId) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        name: true,
                        avatar: true
                    }
                },
                education: {
                    orderBy: { startYear: 'desc' }
                },
                career: {
                    orderBy: { startYear: 'desc' }
                },
                availability: {
                    orderBy: { dayOfWeek: 'asc' }
                },
                _count: {
                    select: {
                        reviews: true,
                        appointments: true
                    }
                },
                reviews: {
                    select: {
                        rating: true
                    }
                }
            }
        });
        if (!tutor) {
            return null;
        }
        // Calculate average rating
        const averageRating = tutor.reviews.length > 0
            ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
            : 0;
        // Remove reviews from the result and add averageRating
        const { reviews, ...tutorWithoutReviews } = tutor;
        return {
            ...tutorWithoutReviews,
            averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal place
        };
    }
    /**
     * Update tutor profile
     */
    static async updateTutorProfile(tutorId, data) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        return await prisma_1.prisma.tutorProfile.update({
            where: { id: tutorId },
            data: {
                title: data.title,
                bio: data.bio,
                rate: data.rate,
                updatedAt: new Date()
            }
        });
    }
    /**
     * Get all tutors with pagination and filtering
     */
    static async getAllTutors(options = {}) {
        const { page = 1, limit = 10, status, search } = options;
        const skip = (page - 1) * limit;
        const where = {};
        if (status) {
            where.status = status;
        }
        if (search) {
            where.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { bio: { contains: search, mode: 'insensitive' } },
                { user: { name: { contains: search, mode: 'insensitive' } } }
            ];
        }
        const [tutors, total] = await Promise.all([
            prisma_1.prisma.tutorProfile.findMany({
                where,
                skip,
                take: limit,
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            name: true,
                            avatar: true
                        }
                    },
                    education: {
                        orderBy: { startYear: 'desc' }
                    },
                    career: {
                        orderBy: { startYear: 'desc' }
                    },
                    availability: {
                        orderBy: { dayOfWeek: 'asc' }
                    },
                    _count: {
                        select: {
                            reviews: true,
                            appointments: true
                        }
                    },
                    reviews: {
                        select: {
                            rating: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            }),
            prisma_1.prisma.tutorProfile.count({ where })
        ]);
        // Calculate average ratings for all tutors
        const tutorsWithRatings = tutors.map(tutor => {
            const averageRating = tutor.reviews.length > 0
                ? tutor.reviews.reduce((sum, review) => sum + review.rating, 0) / tutor.reviews.length
                : 0;
            const { reviews, ...tutorWithoutReviews } = tutor;
            return {
                ...tutorWithoutReviews,
                averageRating: Math.round(averageRating * 10) / 10
            };
        });
        return {
            tutors: tutorsWithRatings,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit)
        };
    }
    /**
     * Approve or reject tutor application (admin only)
     */
    static async updateTutorStatus(tutorId, status) {
        const tutor = await prisma_1.prisma.tutorProfile.findUnique({
            where: { id: tutorId }
        });
        if (!tutor) {
            throw new Error('Tutor profile not found');
        }
        return await prisma_1.prisma.tutorProfile.update({
            where: { id: tutorId },
            data: {
                status,
                updatedAt: new Date()
            }
        });
    }
}
exports.TutorService = TutorService;
