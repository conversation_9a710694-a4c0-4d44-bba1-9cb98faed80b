// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id              String             @id @default(uuid())
  email           String             @unique
  name            String?
  avatar          String?            // User avatar URL
  image           String?            // Better-auth compatibility
  emailVerified   <PERSON>olean            @default(false)
  disabled        <PERSON>olean            @default(false) // User disabled flag
  password        String?
  role            String             @default("user") // user, admin

  // Phone number fields for Better-Auth phone number plugin
  phoneNumber     String?            @unique @map("phone_number")
  phoneNumberVerified Boolean        @default(false) @map("phone_number_verified")

  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @updatedAt @map("updated_at")
  sessions        Session[]
  refreshTokens   RefreshToken[]
  resetTokens     PasswordResetToken[]
  accounts        Account[]          // Better-auth accounts

  // Tutor relations
  tutorProfile    TutorProfile?      // One-to-one relation with tutor profile
  studentAppointments Appointment[] @relation("StudentAppointments") // Appointments as student

  // File relations
  uploadedFiles   File[]             // Files uploaded by this user

  // Payment relations
  studentPayments Payment[]          @relation("StudentPayments") // Payments made as student

  // Verification review relations
  educationVerificationReviews EducationVerification[] @relation("EducationVerificationReviewer")
  careerVerificationReviews CareerVerification[] @relation("CareerVerificationReviewer")

  @@map("users")
  @@index([email])
  @@index([role])
  @@index([phoneNumber])
}

// Better-auth Account model for OAuth providers
model Account {
  id                String  @id @default(uuid())
  userId            String  @map("user_id")
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  accountId         String  @map("account_id") // Better-auth uses accountId
  providerId        String  @map("provider_id") // Better-auth uses providerId
  password          String? @db.Text // For credential provider
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@unique([providerId, accountId])
  @@map("accounts")
  @@index([userId])
}

// Session management
model Session {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  userAgent       String?  @map("user_agent")
  ipAddress       String?  @map("ip_address")
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("sessions")
  @@index([userId])
  @@index([token])
}

// Refresh tokens for long-lived sessions
model RefreshToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("refresh_tokens")
  @@index([userId])
  @@index([token])
}

// Password reset tokens
model PasswordResetToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  used            Boolean  @default(false)
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("password_reset_tokens")
  @@index([userId])
  @@index([token])
}

// Verification codes for email verification, registration, password reset
model VerificationCode {
  id        String   @id @default(uuid())
  email     String
  code      String
  type      String   // 'registration', 'password-reset', 'email-verification'
  expiresAt DateTime @map("expires_at")
  used      Boolean  @default(false)
  createdAt DateTime @default(now()) @map("created_at")

  @@map("verification_codes")
  @@index([email, type])
  @@index([code])
  @@index([expiresAt])
}

model TutorProfile {
  id           String    @id @default(uuid())
  userId      String    @unique @map("user_id")
  title        String?
  bio          String?
  rate         Float?    @default(0.0) // Deprecated, use hourlyRate instead
  hourlyRate   Float?    @default(0.0) @map("hourly_rate") // Price per hour in CNY
  halfHourRate Float?    @default(0.0) @map("half_hour_rate") // Price per 30 minutes in CNY
  isFree       Boolean   @default(false) @map("is_free") // Whether tutor provides free service
  currency     String    @default("CNY") // Currency code
  status       String    @default("pending") // pending, approved, rejected
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  education    TutorEducation[]
  career       TutorCareer[]
  availability TutorAvailability[]
  appointments Appointment[]
  reviews      Review[]
  payments     Payment[] @relation("TutorPayments")
  paymentSplits PaymentSplit[] @relation("TutorSplits")

  @@map("tutors")
}

model TutorEducation {
  id              String      @id @default(uuid())
  tutorId        String
  degree          String
  fieldOfStudy  String
  institution     String
  startYear      Int
  endYear        Int?
  description     String?
  verificationStatus String @default("pending") // pending, approved, rejected
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)
  verificationMaterials EducationVerification[]

  @@map("tutor_education")
}

model TutorCareer {
  id              String      @id @default(uuid())
  tutorId        String
  title           String
  company         String
  startYear      Int
  endYear        Int?
  current         Boolean     @default(false)
  description     String?
  verificationStatus String @default("pending") // pending, approved, rejected
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)
  verificationMaterials CareerVerification[]

  @@map("tutor_career")
}

model TutorAvailability {
  id           String      @id @default(uuid())
  tutorId     String
  dayOfWeek  Int         // 0-6 (Sunday-Saturday)
  startTime   String      // Format: "HH:MM"
  endTime     String      // Format: "HH:MM"
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  tutor       TutorProfile @relation(fields: [tutorId], references: [id], onDelete: Cascade)

  @@map("tutor_availability")
}

model Appointment {
  id              String    @id @default(uuid())
  tutorId        String
  studentId      String
  status          String    @default("scheduled") // scheduled, confirmed, completed, cancelled
  meetingType    String    // online, in_person
  meetingLink    String?
  startTime      DateTime
  endTime        DateTime
  duration        Int       // Duration in minutes
  price           Float     @default(0.0) // Total price for this appointment
  currency        String    @default("CNY") // Currency code
  requiresPayment Boolean   @default(false) @map("requires_payment") // Whether payment is required
  paymentStatus   String    @default("pending") @map("payment_status") // pending, paid, failed, refunded
  confirmationStatus String @default("pending") @map("confirmation_status") // pending, confirmed, rejected
  confirmationToken String? @unique @map("confirmation_token") // Token for email confirmation
  confirmationExpiresAt DateTime? @map("confirmation_expires_at") // When confirmation expires
  notes           String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  tutor          TutorProfile @relation(fields: [tutorId], references: [id])
  student        User         @relation("StudentAppointments", fields: [studentId], references: [id])
  review         Review?
  payment        Payment?

  @@map("appointments")
}

model Review {
  id              String      @id @default(uuid())
  appointmentId  String      @unique
  tutorId       String
  rating          Int         // 1-5
  comment         String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  appointment    Appointment @relation(fields: [appointmentId], references: [id])
  tutor          TutorProfile @relation(fields: [tutorId], references: [id])

  @@map("reviews")
}

// File storage model for S3 uploaded files
model File {
  id              String      @id @default(uuid())
  originalName    String      @map("original_name")
  fileName        String      @map("file_name") // Generated unique filename
  mimeType        String      @map("mime_type")
  size            Int         // File size in bytes
  s3Key           String      @unique @map("s3_key") // S3 object key
  s3Bucket        String      @map("s3_bucket")
  s3Region        String      @map("s3_region")
  cdnUrl          String?     @map("cdn_url") // CDN URL if available
  uploadedById    String      @map("uploaded_by_id")
  category        String      @default("general") // avatar, document, media, etc.
  isPublic        Boolean     @default(false) // Whether file is publicly accessible
  metadata        Json?       // Additional metadata (dimensions, duration, etc.)
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")

  // Relations
  uploadedBy      User        @relation(fields: [uploadedById], references: [id], onDelete: Cascade)
  educationVerifications EducationVerification[] @relation("EducationVerificationFiles")
  careerVerifications CareerVerification[] @relation("CareerVerificationFiles")

  @@map("files")
  @@index([uploadedById])
  @@index([category])
  @@index([s3Key])
  @@index([createdAt])
}

// Education verification materials
model EducationVerification {
  id              String      @id @default(uuid())
  educationId     String
  fileId          String
  materialType    String      // diploma, transcript, certificate, etc.
  description     String?
  status          String      @default("pending") // pending, approved, rejected
  reviewedById    String?     // Admin who reviewed
  reviewedAt      DateTime?
  reviewNotes     String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  education       TutorEducation @relation(fields: [educationId], references: [id], onDelete: Cascade)
  file            File        @relation("EducationVerificationFiles", fields: [fileId], references: [id], onDelete: Cascade)
  reviewedBy      User?       @relation("EducationVerificationReviewer", fields: [reviewedById], references: [id])

  @@map("education_verification")
  @@index([educationId])
  @@index([status])
}

// Career verification materials
model CareerVerification {
  id              String      @id @default(uuid())
  careerId        String
  fileId          String
  materialType    String      // work_certificate, business_card, contract, etc.
  description     String?
  status          String      @default("pending") // pending, approved, rejected
  reviewedById    String?     // Admin who reviewed
  reviewedAt      DateTime?
  reviewNotes     String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  career          TutorCareer @relation(fields: [careerId], references: [id], onDelete: Cascade)
  file            File        @relation("CareerVerificationFiles", fields: [fileId], references: [id], onDelete: Cascade)
  reviewedBy      User?       @relation("CareerVerificationReviewer", fields: [reviewedById], references: [id])

  @@map("career_verification")
  @@index([careerId])
  @@index([status])
}

// Payment model for handling tutor service payments
model Payment {
  id              String      @id @default(uuid())
  appointmentId   String      @unique @map("appointment_id")
  tutorId         String      @map("tutor_id")
  studentId       String      @map("student_id")
  amount          Float       // Total payment amount
  currency        String      @default("CNY")
  status          String      @default("pending") // pending, processing, completed, failed, refunded
  paymentMethod   String      @default("wechat") @map("payment_method") // wechat, alipay, etc.
  transactionId   String?     @unique @map("transaction_id") // External payment system transaction ID
  wechatOrderId   String?     @unique @map("wechat_order_id") // WeChat payment order ID
  paidAt          DateTime?   @map("paid_at")
  refundedAt      DateTime?   @map("refunded_at")
  splitProcessedAt DateTime?  @map("split_processed_at") // When payment was split to tutor
  metadata        Json?       // Additional payment metadata
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")

  // Relations
  appointment     Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  tutor           TutorProfile @relation("TutorPayments", fields: [tutorId], references: [id])
  student         User        @relation("StudentPayments", fields: [studentId], references: [id])
  splits          PaymentSplit[]

  @@map("payments")
  @@index([status])
  @@index([tutorId])
  @@index([studentId])
  @@index([createdAt])
}

// Payment split model for tracking tutor commission
model PaymentSplit {
  id              String      @id @default(uuid())
  paymentId       String      @map("payment_id")
  tutorId         String      @map("tutor_id")
  tutorAmount     Float       @map("tutor_amount") // Amount going to tutor
  platformAmount  Float       @map("platform_amount") // Amount kept by platform
  tutorPercentage Float       @map("tutor_percentage") // Percentage for tutor (e.g., 0.7 for 70%)
  status          String      @default("pending") // pending, processing, completed, failed
  transferredAt   DateTime?   @map("transferred_at") // When money was transferred to tutor
  wechatTransferId String?    @unique @map("wechat_transfer_id") // WeChat transfer ID
  failureReason   String?     @map("failure_reason")
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")

  // Relations
  payment         Payment     @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  tutor           TutorProfile @relation("TutorSplits", fields: [tutorId], references: [id])

  @@map("payment_splits")
  @@index([paymentId])
  @@index([tutorId])
  @@index([status])
}