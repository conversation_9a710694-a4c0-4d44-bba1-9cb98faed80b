
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import TutorRegister from "./pages/TutorRegister";
import Feedback from "./pages/Feedback";
import AdminDashboard from "./pages/AdminDashboard";
import Tutors from "./pages/Tutors";
import TutorProfile from "./pages/TutorProfile";
import Appointments from "./pages/Appointments";
import BookingConfirmation from "./pages/BookingConfirmation";
import My from "./pages/My";
import Profile from "./pages/Profile";
import UserAgreement from "./pages/UserAgreement";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import NotFound from "./pages/NotFound";
import Help from "./pages/Help";
import FileManager from "./pages/FileManager";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/login" element={<Login />} />
          <Route path="/tutor-register" element={<TutorRegister />} />
          <Route path="/feedback" element={<Feedback />} />
          <Route path="/admin" element={<AdminDashboard />} />
          <Route path="/tutors" element={<Tutors />} />
          <Route path="/tutor-profile" element={<TutorProfile />} />
          <Route path="/appointments" element={<Appointments />} />
          <Route path="/booking-confirmation" element={<BookingConfirmation />} />
          <Route path="/my" element={<My />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/user-agreement" element={<UserAgreement />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/help" element={<Help />} />
          <Route path="/files" element={<FileManager />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
